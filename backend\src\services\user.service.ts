import { db } from '../config/database';
import { hashPassword, comparePassword } from '../utils/hash.util';
import type { UpdateUserRequest, ChangePasswordRequest, UpdateUserByAdminRequest } from '../types/user.types';
import type { PaginationOptions, PaginatedResponse } from '../types/common.types';

export class UserService {
  static async getAllUsers(options: PaginationOptions = {}) {
    const { page = 1, limit = 10 } = options;
    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      db.user.findMany({
        skip,
        take: limit,
        select: {
          id: true,
          email: true,
          username: true,
          name: true,
          role: true,
          createdAt: true,
        },
        orderBy: { createdAt: 'desc' }
      }),
      db.user.count()
    ]);

    return {
      data: users,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        page,
        limit,
      }
    } as PaginatedResponse<any>;
  }

  static async getUserById(userId: number) {
    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        username: true,
        name: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    if (!user) {
      throw new Error('User not found');
    }

    return user;
  }

  static async updateUser(userId: number, data: UpdateUserRequest) {
    // Check if username is taken (if provided)
    if (data.username) {
      const existingUser = await db.user.findFirst({
        where: {
          username: data.username,
          NOT: { id: userId }
        }
      });

      if (existingUser) {
        throw new Error('Username is already taken');
      }
    }

    const user = await db.user.update({
      where: { id: userId },
      data,
      select: {
        id: true,
        email: true,
        username: true,
        name: true,
        role: true,
        updatedAt: true,
      }
    });

    return user;
  }

  static async changePassword(userId: number, data: ChangePasswordRequest) {
    // Get current user
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { password: true }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await comparePassword(data.currentPassword, user.password);
    
    if (!isCurrentPasswordValid) {
      throw new Error('Current password is incorrect');
    }

    // Hash new password
    const hashedNewPassword = await hashPassword(data.newPassword);

    // Update password
    await db.user.update({
      where: { id: userId },
      data: { password: hashedNewPassword }
    });

    return true;
  }

  // Admin functions
  static async updateUserByAdmin(userId: number, data: UpdateUserByAdminRequest) {
    // Check if email or username is taken (if provided)
    if (data.email || data.username) {
      const conditions = [];
      if (data.email) conditions.push({ email: data.email });
      if (data.username) conditions.push({ username: data.username });

      const existingUser = await db.user.findFirst({
        where: {
          OR: conditions,
          NOT: { id: userId }
        }
      });

      if (existingUser) {
        throw new Error('Email or username is already taken');
      }
    }

    const user = await db.user.update({
      where: { id: userId },
      data,
      select: {
        id: true,
        email: true,
        username: true,
        name: true,
        role: true,
        updatedAt: true,
      }
    });

    return user;
  }

  static async hardDeleteUser(userId: number) {
    // Hard delete - permanently remove from database
    await db.user.delete({
      where: { id: userId }
    });

    return true;
  }

  static async searchUsers(query: string, options: PaginationOptions = {}) {
    const { page = 1, limit = 10 } = options;
    const skip = (page - 1) * limit;

    const [users, total] = await Promise.all([
      db.user.findMany({
        where: {
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { username: { contains: query, mode: 'insensitive' } },
            { email: { contains: query, mode: 'insensitive' } },
          ],
        },
        skip,
        take: limit,
        select: {
          id: true,
          email: true,
          username: true,
          name: true,
          role: true,
          createdAt: true,
        },
        orderBy: { createdAt: 'desc' }
      }),
      db.user.count({
        where: {
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { username: { contains: query, mode: 'insensitive' } },
            { email: { contains: query, mode: 'insensitive' } },
          ],
        }
      })
    ]);

    return {
      data: users,
      pagination: {
        total,
        pages: Math.ceil(total / limit),
        page,
        limit,
      }
    } as PaginatedResponse<any>;
  }
}