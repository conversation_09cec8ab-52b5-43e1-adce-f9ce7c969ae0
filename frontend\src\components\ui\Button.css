/* Button Component Styles */
.btn {
  border: none;
  padding: 12px 24px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  outline: none;
}

.btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Primary Button */
.btn--primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
}

.btn--primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Secondary Button */
.btn--secondary {
  background: var(--secondary-color);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.btn--secondary:hover:not(:disabled) {
  background: var(--background-muted);
  border-color: var(--border-light);
}

/* Danger Button */
.btn--danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%);
  color: white;
}

.btn--danger:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* Size variants */
.btn--small {
  padding: 8px 16px;
  font-size: 14px;
}

.btn--large {
  padding: 16px 32px;
  font-size: 18px;
}

/* Full width */
.btn--full {
  width: 100%;
}

/* Settings button styling */
.settings-options .btn {
  justify-content: flex-start;
  padding: 8px 16px;
  font-size: 14px;
  text-align: left;
  margin-bottom: 8px;
}
