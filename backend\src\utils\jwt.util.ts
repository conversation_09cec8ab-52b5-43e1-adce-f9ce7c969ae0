import { sign, verify } from 'hono/jwt';
import { env } from '../config/env';

export interface JWTPayload {
  userId: number;
  email: string;
  role: string;
}

export const generateToken = async (payload: JWTPayload): Promise<string> => {
  return await sign({
    ...payload,
    exp: Math.floor(Date.now() / 1000) + (60 * 60 * 24 * 7), // 7 days
  }, env.JWT_SECRET);
};

export const verifyToken = async (token: string): Promise<JWTPayload> => {
  try {
    const payload = await verify(token, env.JWT_SECRET) as any;
    return {
      userId: payload.userId,
      email: payload.email,
      role: payload.role,
    };
  } catch (error) {
    throw new Error('Invalid token');
  }
};