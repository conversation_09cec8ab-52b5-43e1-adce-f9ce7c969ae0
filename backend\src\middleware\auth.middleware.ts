import { createMiddleware } from "hono/factory";
import { jwt } from 'hono/jwt';
import { env } from '../config/env';
import { db } from '../config/database';
import { errorResponse } from '../utils/response.util';

export const authMiddleware = jwt({
  secret: env.JWT_SECRET,
});

export const requireAuth = createMiddleware(async (c, next) => {
  try {
    const payload = c.get('jwtPayload');
    
    if (!payload) {
      return c.json(errorResponse('Authentication required'), 401);
    }

    // Check if user still exists
    const user = await db.user.findUnique({
      where: { id: payload.userId },
      select: { id: true, email: true, role: true},
    });

    if (!user) {
      return c.json(errorResponse('User not found'), 401);
    }

    c.set('user', user);
    await next();
  } catch (error) {
    return c.json(errorResponse('Authentication failed'), 401);
  }
});

export const requireAdmin = createMiddleware(async (c, next) => {
  const user = c.get('user');
  
  if (!user || user.role !== 'ADMIN') {
    return c.json(errorResponse('Admin access required'), 403);
  }

  await next();
});