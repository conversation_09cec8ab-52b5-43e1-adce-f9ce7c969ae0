import type { Context } from 'hono';
import { AuthService } from '../services/auth.service';
import { successResponse, errorResponse } from '../utils/response.util';

export class AuthController {
  static async register(c: Context) {
    try {
      const data = c.get('validatedData');
      const result = await AuthService.register(data);
      
      return c.json(successResponse(result, 'User registered successfully'), 201);
    } catch (error: any) {
      return c.json(errorResponse(error.message), 400);
    }
  }

  static async login(c: Context) {
    try {
      const data = c.get('validatedData');
      const result = await AuthService.login(data);
      
      return c.json(successResponse(result, 'Login successful'));
    } catch (error: any) {
      return c.json(errorResponse(error.message), 401);
    }
  }

  static async profile(c: Context) {
    try {
      const user = c.get('user');
      const profile = await AuthService.getProfile(user.id);

      return c.json(successResponse(profile, 'Profile retrieved successfully'));
    } catch (error: any) {
      return c.json(errorResponse(error.message), 400);
    }
  }

  static async logout(c: Context) {
    // ใน JWT ไม่จำเป็นต้องทำอะไรในการ logout
    // สามารถเพิ่ม blacklist หรือ refresh token ได้
    return c.json(successResponse(null, 'Logout successful'));
  }
}