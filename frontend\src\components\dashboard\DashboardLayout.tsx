import React from 'react'
import DashboardNav from './DashboardNav'
import './DashboardLayout.css'

interface DashboardLayoutProps {
  userName?: string
  onLogout: () => void
  children: React.ReactNode
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  userName = 'User',
  onLogout,
  children
}) => {
  return (
    <div className="dashboard-container">
      <DashboardNav userName={userName} onLogout={onLogout} />
      
      <main className="dashboard-main">
        <div className="dashboard-header">
          <h1>Welcome to Your Dashboard</h1>
          <p>Manage your account and access your features here.</p>
        </div>
        
        <div className="dashboard-content">
          {children}
        </div>
      </main>
    </div>
  )
}

export default DashboardLayout
