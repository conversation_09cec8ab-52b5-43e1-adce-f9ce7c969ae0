/* Global Styles and CSS Reset */
@import './variables.css';

/* CSS Reset and Base Styles */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--font-family);
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-normal);
  color: var(--text-primary);
  background-color: var(--background-secondary);
  min-width: 320px;
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
}

/* App Container */
.app {
  min-height: 100vh;
  font-family: var(--font-family);
  line-height: var(--line-height-relaxed);
  color: var(--text-primary);
  width: 100%;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  line-height: var(--line-height-tight);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

h1 {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
}

h2 {
  font-size: var(--font-size-3xl);
}

h3 {
  font-size: var(--font-size-2xl);
}

h4 {
  font-size: var(--font-size-xl);
}

h5 {
  font-size: var(--font-size-lg);
}

h6 {
  font-size: var(--font-size-base);
}

p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
}

/* Links */
a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-normal);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Form elements */
input, 
button, 
textarea, 
select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button {
  cursor: pointer;
  border: none;
  outline: none;
  background: none;
  padding: 0;
}

button:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Lists */
ul, ol {
  list-style: none;
}

/* Tables */
table {
  border-collapse: collapse;
  width: 100%;
}

/* Accessibility - Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus visible for better accessibility */
.focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Utility Classes */
.visually-hidden {
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* Dark theme */
[data-theme="dark"] {
  --text-primary: #f8f9fa;
  --text-secondary: #e9ecef;
  --text-muted: #adb5bd;
  --text-light: #6c757d;
  
  --background-primary: #1a1a1a;
  --background-secondary: #2d3748;
  --background-muted: #4a5568;
  
  --border-color: #4a5568;
  --border-light: #6c757d;
  
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.5);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.6);
}

/* Light theme (default) */
[data-theme="light"] {
  --text-primary: #333;
  --text-secondary: #666;
  --text-muted: #6b7280;
  --text-light: #999;
  
  --background-primary: #ffffff;
  --background-secondary: #f8f9fa;
  --background-muted: #fafbfc;
  
  --border-color: #e1e5e9;
  --border-light: #f0f0f0;
  
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive breakpoints */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }
  
  h1 {
    font-size: var(--font-size-3xl);
  }
  
  h2 {
    font-size: var(--font-size-2xl);
  }
}

@media (max-width: 480px) {
  html {
    font-size: 14px;
  }
  
  .container {
    padding: 0 var(--spacing-xs);
  }
  
  h1 {
    font-size: var(--font-size-2xl);
  }
}
