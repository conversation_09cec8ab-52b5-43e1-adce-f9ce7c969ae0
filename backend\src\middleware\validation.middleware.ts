import { createMiddleware } from "hono/factory";
import { Zod<PERSON>rror, z } from "zod";
import { errorResponse } from "../utils/response.util";

export const validate = (schema: z.ZodType) => {
  return createMiddleware(async (c, next) => {
    try 
    {
      const body = await c.req.json();
      const validatedData = schema.parse(body);
      c.set("validatedData", validatedData);

      await next();
    } 
    catch (error) 
    {
      if (error instanceof ZodError) {
        return c.json(errorResponse("Validation failed", error.issues), 400);
      }
      return c.json(errorResponse("Invalid request data"), 400);
    }
  });
};
