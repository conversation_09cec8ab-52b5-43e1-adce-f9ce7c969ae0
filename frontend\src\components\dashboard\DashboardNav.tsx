import React from 'react'
import But<PERSON> from '../ui/Button'
import './DashboardNav.css'

interface DashboardNavProps {
  userName?: string
  onLogout: () => void
}

const DashboardNav: React.FC<DashboardNavProps> = ({
  userName = 'User',
  onLogout
}) => {
  return (
    <nav className="dashboard-nav">
      <div className="nav-brand">
        <h2>Dashboard</h2>
      </div>
      <div className="nav-actions">
        <span className="user-info">Welcome, {userName}!</span>
        <Button 
          onClick={onLogout}
          variant="danger"
          className="logout-button"
        >
          Logout
        </Button>
      </div>
    </nav>
  )
}

export default DashboardNav
