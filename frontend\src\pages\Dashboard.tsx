import React from 'react'
import { DashboardLayout, DashboardCard, Button } from '../components'

interface DashboardProps {
  setIsAuthenticated: (auth: boolean) => void
}

const Dashboard: React.FC<DashboardProps> = ({ setIsAuthenticated }) => {
  const handleLogout = () => {
    localStorage.removeItem('token')
    setIsAuthenticated(false)
  }

  return (
    <DashboardLayout userName="Test User" onLogout={handleLogout}>
      <div className="dashboard-cards">
        <DashboardCard
          icon="👤"
          title="Profile"
          description="Manage your personal information"
        >
          <div className="user-details">
            <p><strong>Name:</strong> Test User</p>
            <p><strong>Email:</strong> <EMAIL></p>
          </div>
        </DashboardCard>

        <DashboardCard
          icon="📊"
          title="Analytics"
          description="View your usage statistics"
        >
          <div className="stats">
            <div className="stat-item">
              <span className="stat-number">24</span>
              <span className="stat-label">Total Logins</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">7</span>
              <span className="stat-label">Days Active</span>
            </div>
          </div>
        </DashboardCard>

        <DashboardCard
          icon="⚙️"
          title="Settings"
          description="Configure your preferences"
        >
          <div className="settings-options">
            <Button variant="secondary">Update Profile</Button>
            <Button variant="secondary">Change Password</Button>
          </div>
        </DashboardCard>
      </div>
    </DashboardLayout>
  )
}

export default Dashboard
