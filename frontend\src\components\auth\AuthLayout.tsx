import React from 'react'
import './AuthLayout.css'

interface AuthLayoutProps {
  title: string
  subtitle: string
  children: React.ReactNode
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ title, subtitle, children }) => {
  return (
    <div className="auth-container">
      <div className="auth-card">
        <div className="auth-header">
          <h1>{title}</h1>
          <p>{subtitle}</p>
        </div>
        {children}
      </div>
    </div>
  )
}

export default AuthLayout
