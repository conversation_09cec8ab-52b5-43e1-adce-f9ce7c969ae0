import { db } from '../config/database';
import { hashPassword, comparePassword } from '../utils/hash.util';
import { generateToken } from '../utils/jwt.util';
import type { LoginRequest, RegisterRequest } from '../types/auth.types';

export class AuthService {

  static async register(data: RegisterRequest) {
    // Check if user exists
    const existingUser = await db.user.findFirst({
      where: {
        OR: [
          { email: data.email },
          { username: data.username }
        ]
      }
    });

    if (existingUser) {
      throw new Error('User with this email or username already exists');
    }

    // Hash password
    const hashedPassword = await hashPassword(data.password);

    // Create user
    const user = await db.user.create({
      data: {
        ...data,
        password: hashedPassword,
      },
      select: {
        id: true,
        email: true,
        username: true,
        name: true,
        role: true,
        createdAt: true,
      }
    });

    // Generate token
    const token = await generateToken({
      userId: user.id,
      email: user.email || '',
      role: user.role,
    });

    return { user, token };
  }

  static async login(data: LoginRequest) {
    // Find user
    const user = await db.user.findUnique({
      where: { username: data.username }
    });

    if (!user) {
      throw new Error('User not found');
    }

    // Check password
    const isPasswordValid = await comparePassword(data.password, user.password);
    
    if (!isPasswordValid) {
      throw new Error('Invalid credentials');
    }

    // Generate token
    const token = await generateToken({
      userId: user.id,
      email: user.email || '',
      role: user.role,
    });

    const { password, ...userWithoutPassword } = user;

    return { user: userWithoutPassword, token };
  }

  static async getProfile(userId: number) {
    return await db.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        username: true,
        name: true,
        role: true,
        createdAt: true,
      }
    });
  }
}