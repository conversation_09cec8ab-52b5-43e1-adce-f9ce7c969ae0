/* Input Component Styles */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.form-label {
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  margin-bottom: 4px;
}

.form-input {
  padding: 14px 16px;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  transition: all var(--transition-normal);
  background-color: var(--background-primary);
  color: var(--text-primary);
  font-family: inherit;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  background-color: var(--background-primary);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input::placeholder {
  color: var(--text-muted);
  opacity: 1;
}

/* Error state */
.form-input--error {
  border-color: var(--error-color);
}

.form-input--error:focus {
  border-color: var(--error-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-error {
  color: var(--error-color);
  font-size: var(--font-size-sm);
  margin-top: 4px;
}

/* Responsive */
@media (max-width: 480px) {
  .form-input {
    padding: 12px 14px;
    font-size: 16px; /* Prevents zoom on iOS */
  }
}
