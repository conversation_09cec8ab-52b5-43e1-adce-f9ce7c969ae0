import { Hono } from 'hono';
import { serve } from 'bun';
import { env } from './config/env';
import router from '../src/routes';
import { loggerMiddleware } from './middleware/logger.middleware';
import { corsMiddleware } from './middleware/cors.middleware';
import { successResponse, errorResponse } from './utils/response.util';
import { showRoutes } from 'hono/dev';

const app = new Hono();

// Global middleware
app.use('*', loggerMiddleware);
app.use('*', corsMiddleware);

app.route('/api', router);

app.get('/', (c) => {
  return c.text('Hello, world!');
});

// Health check
app.get('/health', (c) => {
  return c.json(successResponse({ status: 'ok', timestamp: new Date().toISOString() }));
});

// 404 handler
app.notFound((c) => {
  return c.json(errorResponse('Route not found'), 404);
});

// Error handler
app.onError((err, c) => {
  console.error(`${err}`);
  return c.json(errorResponse('Internal server error'), 500);
});

const port = env.PORT
console.log(`Server is running on http://localhost:${port}`)

showRoutes(app)

serve({
  fetch: app.fetch,
  port
})