import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import { AuthLayout, Button, Input } from '../components'
import { authAPI, type LoginData } from '../services/api'

interface LoginProps {
  setIsAuthenticated: (auth: boolean) => void
}

const Login: React.FC<LoginProps> = ({ setIsAuthenticated }) => {
  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    if (!username || !password) {
      setError("Please enter username and password");
      setLoading(false);
      return;
    }

    try {
      const loginData: LoginData = { username, password };
      const response = await authAPI.login(loginData);

      // Store the token if provided
      if (response.token) {
        localStorage.setItem("token", response.token);
      }

      // Set authentication state
      setIsAuthenticated(true);

      console.log("Login successful:", response);
    } catch (error) {
      setError(error instanceof Error ? error.message : "Login failed");
      console.log("Login error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthLayout 
      title="Welcome Back"
      subtitle="Sign in to your account"
    >
      <form className="auth-form" onSubmit={handleSubmit}>
        {error && <div className="error-message">{error}</div>}
        
        <Input
          type="text"
          label="Username"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          required
          placeholder="Enter your username"
        />

        <Input
          type="password"
          label="Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          placeholder="Enter your password"
        />

        <Button
          type="submit"
          disabled={loading}
          variant="primary"
        >
          {loading ? 'Signing in...' : 'Sign In'}
        </Button>
      </form>

      <div className="auth-footer">
        <p>
          Don't have an account?{' '}
          <Link to="/register" className="auth-link">
            Sign up
          </Link>
        </p>
      </div>
    </AuthLayout>
  )
}

export default Login
