import { Hono } from 'hono';
import { validate } from '../middleware/validation.middleware';
import { authMiddleware, requireAuth, requireAdmin } from '../middleware/auth.middleware';
import { UserController } from '../controllers/user.controller';
import { 
  updateUserSchema, 
  changePasswordSchema, 
  updateUserByAdminSchema 
} from '../types/user.types';

const users = new Hono();

// Apply auth middleware to all user routes
users.use('*', authMiddleware);

// Public user routes (require auth but accessible to all authenticated users)
users.get('/search', requireAuth, UserController.searchUsers);
users.get('/me', requireAuth, UserController.getCurrentUser);
users.put('/me', requireAuth, validate(updateUserSchema), UserController.updateCurrentUser);
users.put('/me/password', requireAuth, validate(changePasswordSchema), UserController.changePassword);
users.delete('/me', requireAuth, UserController.deleteCurrentUser);

// Get specific user (public profile)
users.get('/:id', requireAuth, UserController.getUserById);

// Admin only routes
users.get('/', requireAuth, requireAdmin, UserController.getAllUsers);
users.put('/:id', requireAuth, requireAdmin, validate(updateUserByAdminSchema), UserController.updateUserByAdmin);
users.delete('/:id', requireAuth, requireAdmin, UserController.deleteUserByAdmin);

export default users;