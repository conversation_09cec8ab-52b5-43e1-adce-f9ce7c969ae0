import React from 'react'
import './DashboardCard.css'

interface DashboardCardProps {
  icon: string
  title: string
  description: string
  children?: React.ReactNode
  className?: string
}

const DashboardCard: React.FC<DashboardCardProps> = ({
  icon,
  title,
  description,
  children,
  className = ''
}) => {
  return (
    <div className={`dashboard-card ${className}`}>
      <div className="card-icon">{icon}</div>
      <div className="card-content">
        <h3>{title}</h3>
        <p>{description}</p>
        {children}
      </div>
    </div>
  )
}

export default DashboardCard
