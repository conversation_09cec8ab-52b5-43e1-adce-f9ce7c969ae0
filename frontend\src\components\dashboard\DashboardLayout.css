/* Dashboard Layout Styles */
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.dashboard-main {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 3rem;
}

.dashboard-header h1 {
  color: #333;
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 10px;
}

.dashboard-header p {
  color: #666;
  font-size: 18px;
  margin: 0;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.dashboard-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 2rem;
}

/* Stats Components */
.user-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-details p {
  margin: 0;
  color: #555;
  font-size: 14px;
}

.user-details strong {
  color: #333;
  font-weight: 600;
}

.stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
  display: block;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.settings-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Responsive Design */
@media (min-width: 1400px) {
  .dashboard-main {
    max-width: 1400px;
  }
  
  .dashboard-cards {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .dashboard-header h1 {
    font-size: 42px;
  }
}

@media (min-width: 992px) and (max-width: 1399px) {
  .dashboard-cards {
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
  }
}

@media (min-width: 769px) and (max-width: 991px) {
  .dashboard-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .dashboard-main {
    padding: 1.5rem;
  }
}

@media (max-width: 768px) {
  .dashboard-main {
    padding: 1rem;
  }
  
  .dashboard-header h1 {
    font-size: 28px;
  }
  
  .dashboard-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .stats {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .dashboard-main {
    padding: 0.5rem;
  }
  
  .dashboard-header h1 {
    font-size: 24px;
  }
  
  .dashboard-header p {
    font-size: 16px;
  }
}
