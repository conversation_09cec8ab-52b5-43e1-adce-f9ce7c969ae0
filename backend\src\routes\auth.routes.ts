import { Hono } from 'hono';
import { validate } from '../middleware/validation.middleware';
import { authMiddleware, requireAuth } from '../middleware/auth.middleware';
import { AuthController } from '../controllers/auth.controller';
import { loginSchema, registerSchema } from '../types/auth.types';

const auth = new Hono();

auth.post('/register', validate(registerSchema), AuthController.register);
auth.post('/login', validate(loginSchema), AuthController.login);
auth.post('/logout', AuthController.logout);

// Protected routes
auth.use('/profile', authMiddleware);
auth.get('/profile', requireAuth, AuthController.profile);

export default auth;