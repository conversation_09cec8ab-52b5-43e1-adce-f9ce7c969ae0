import type { Context } from 'hono';
import { UserService } from '../services/user.service';
import { successResponse, errorResponse } from '../utils/response.util';

export class UserController {
  static async getAllUsers(c: Context) {
    try {
      const page = Number(c.req.query('page')) || 1;
      const limit = Number(c.req.query('limit')) || 10;

      const result = await UserService.getAllUsers({ page, limit });
      
      return c.json(successResponse(result, 'Users retrieved successfully'));
    } catch (error: any) {
      return c.json(errorResponse(error.message), 400);
    }
  }

  static async getUserById(c: Context) {
    try {
      const userId = c.req.param('id');
      const user = await UserService.getUserById(Number(userId));
      
      return c.json(successResponse(user, 'User retrieved successfully'));
    } catch (error: any) {
      return c.json(errorResponse(error.message), 404);
    }
  }

  static async getCurrentUser(c: Context) {
    try {
      const currentUser = c.get('user');
      const user = await UserService.getUserById(currentUser.id);
      
      return c.json(successResponse(user, 'Current user retrieved successfully'));
    } catch (error: any) {
      return c.json(errorResponse(error.message), 400);
    }
  }

  static async updateCurrentUser(c: Context) {
    try {
      const currentUser = c.get('user');
      const data = c.get('validatedData');
      
      const user = await UserService.updateUser(currentUser.id, data);
      
      return c.json(successResponse(user, 'User updated successfully'));
    } catch (error: any) {
      return c.json(errorResponse(error.message), 400);
    }
  }

  static async changePassword(c: Context) {
    try {
      const currentUser = c.get('user');
      const data = c.get('validatedData');
      
      await UserService.changePassword(currentUser.id, data);
      
      return c.json(successResponse(null, 'Password changed successfully'));
    } catch (error: any) {
      return c.json(errorResponse(error.message), 400);
    }
  }

  static async deleteCurrentUser(c: Context) {
    try {
      const currentUser = c.get('user');
      
      await UserService.hardDeleteUser(currentUser.id);
      
      return c.json(successResponse(null, 'Account deleted successfully'));
    } catch (error: any) {
      return c.json(errorResponse(error.message), 400);
    }
  }

  // Admin functions
  static async updateUserByAdmin(c: Context) {
    try {
      const userId = c.req.param('id');
      const data = c.get('validatedData');
      
      const user = await UserService.updateUserByAdmin(Number(userId), data);
      
      return c.json(successResponse(user, 'User updated successfully'));
    } catch (error: any) {
      return c.json(errorResponse(error.message), 400);
    }
  }

  static async deleteUserByAdmin(c: Context) {
    try {
      const userId = c.req.param('id'); 
       await UserService.hardDeleteUser(Number(userId));
        return c.json(successResponse(null, 'User permanently deleted'));

    } catch (error: any) {
      return c.json(errorResponse(error.message), 400);
    }
  }

  static async searchUsers(c: Context) {
    try {
      const query = c.req.query('q') || '';
      const page = Number(c.req.query('page')) || 1;
      const limit = Number(c.req.query('limit')) || 10;

      if (!query.trim()) {
        return c.json(errorResponse('Search query is required'), 400);
      }

      const result = await UserService.searchUsers(query, { page, limit });
      
      return c.json(successResponse(result, 'Search results retrieved successfully'));
    } catch (error: any) {
      return c.json(errorResponse(error.message), 400);
    }
  }
}