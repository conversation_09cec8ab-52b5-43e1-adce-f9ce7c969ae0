import { z } from 'zod';

export const updateUserSchema = z.object({
  name: z.string().optional(),
  username: z.string().min(3, 'Username must be at least 3 characters').optional(),
  avatar: z.url('Avatar must be a valid URL').optional(),
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(6, 'Current password is required'),
  newPassword: z.string().min(6, 'New password must be at least 6 characters'),
  confirmPassword: z.string().min(6, 'Password confirmation is required'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const updateUserByAdminSchema = z.object({
  name: z.string().optional(),
  username: z.string().min(3).optional(),
  email: z.email().optional(),
  role: z.enum(['USER', 'ADMIN']).optional(),
  isActive: z.boolean().optional(),
});

export type UpdateUserRequest = z.infer<typeof updateUserSchema>;
export type ChangePasswordRequest = z.infer<typeof changePasswordSchema>;
export type UpdateUserByAdminRequest = z.infer<typeof updateUserByAdminSchema>;